# find_tok_symb_sh1.py
import pandas as pd
import requests
import zipfile
import os
from functools import wraps

def safe_run(func):
    """Decorator to catch and log exceptions gracefully."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            print(f"[Error in {func.__name__}]: {e}")
            return None
    return wrapper

class TradingData:
    def __init__(self, data_dir="trading_data"):
        self.data_dir = data_dir
        os.makedirs(self.data_dir, exist_ok=True)
        self.urls = {
            "NSE": "https://api.shoonya.com/NSE_symbols.txt.zip",
            "BSE": "https://api.shoonya.com/BSE_symbols.txt.zip",
            "NFO": "https://api.shoonya.com/NFO_symbols.txt.zip",
            "BFO": "https://api.shoonya.com/BFO_symbols.txt.zip",
            "MCX": "https://api.shoonya.com/MCX_symbols.txt.zip"
        }
        self.dataframes = {}

    @safe_run
    def download_and_extract(self):
        """Downloads and extracts trading data if not already available."""
        for market, url in self.urls.items():
            csv_path = os.path.join(self.data_dir, f"{market}.csv")
            if os.path.exists(csv_path):
                print(f"[✓] {market} CSV already exists.")
                continue

            zip_path = os.path.join(self.data_dir, f"{market}.zip")
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            with open(zip_path, "wb") as f:
                f.write(response.content)

            with zipfile.ZipFile(zip_path, "r") as zip_ref:
                zip_ref.extractall(self.data_dir)
                txt_file = next((f for f in zip_ref.namelist() if f.endswith(".txt")), None)

            if not txt_file:
                print(f"[✗] No .txt file found in {market} archive.")
                continue

            txt_path = os.path.join(self.data_dir, txt_file)
            try:
                df = pd.read_csv(txt_path, delimiter=",", encoding="utf-8")
            except Exception:
                df = pd.read_csv(txt_path, delimiter="|", encoding="utf-8")

            df.to_csv(csv_path, index=False)
            print(f"[✓] Saved {market} data to {csv_path}")

    @safe_run
    def load_data(self):
        """Loads CSVs into memory and normalizes column names."""
        self.dataframes = {
            market: self._load_csv(os.path.join(self.data_dir, f"{market}.csv"))
            for market in self.urls
            if os.path.exists(os.path.join(self.data_dir, f"{market}.csv"))
        }

    def _load_csv(self, path):
        df = pd.read_csv(path)
        df.columns = [col.strip().upper() for col in df.columns]
        df["TOKEN"] = df["TOKEN"].astype(str) if "TOKEN" in df.columns else None
        return df

    def _market_iterator(self):
        """Generator to iterate over available markets and DataFrames."""
        for market, df in self.dataframes.items():
            yield market, df

    def search_symbol(self, symbol):
        """Returns token for a given trading symbol."""
        symbol = symbol.upper()
        for market, df in self._market_iterator():
            symbol_col = next((col for col in ["TRADINGSYMBOL", "SYMBOL"] if col in df.columns), None)
            if symbol_col and "TOKEN" in df.columns:
                match = df[df[symbol_col].str.upper() == symbol]
                if not match.empty:
                    token = match["TOKEN"].values[0]
                    print(f"[✓] Symbol '{symbol}' found in {market} → Token: {token}")
                    return token
        print(f"[✗] Symbol '{symbol}' not found.")
        return None

    def search_by_token(self, token):
        """Returns trading symbol for a given token."""
        token = str(token).strip()
        for market, df in self._market_iterator():
            if "TOKEN" in df.columns:
                match = df[df["TOKEN"] == token]
                if not match.empty:
                    symbol_col = next((col for col in ["TRADINGSYMBOL", "SYMBOL"] if col in match.columns), None)
                    if symbol_col:
                        symbol = match[symbol_col].values[0]
                        print(f"[✓] Token '{token}' found in {market} → Symbol: {symbol}")
                        return symbol
        print(f"[✗] Token '{token}' not found.")
        return None
    
# ------------------ Usage ------------------ #
if __name__ == "__main__":
    trading_data = TradingData()
    trading_data.download_and_extract()  # Download and extract data (or use local CSVs if available)
    trading_data.load_data()             # Load CSV files into memory

    user_symbol = input("Enter the trading symbol (e.g., CRUDEOIL18JUN25): ").strip()
    token = trading_data.search_symbol(user_symbol)
    print(f"Token: {token}")