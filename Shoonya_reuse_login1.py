from NorenRestApiPy.NorenApi import NorenApi
import json
import requests as rq
import pandas as pd
import time
import datetime as dt
from pyotp import TOTP
import keyboard  # For detecting key press

def login():
    class ShoonyaApiPy(NorenApi):
        def __init__(self):
            NorenApi.__init__(self, host='https://api.shoonya.com/NorenWClientTP/',
                              websocket='wss://api.shoonya.com/NorenWSTP/')
    
    api = ShoonyaApiPy()
    with open("Cread.json", "r") as f:
        Cread = json.load(f)
    
    user = Cread["user_id"]
    pwd = Cread["password"]
    
    accessToken = open('session_token.txt', 'r').read()
    api.set_session(user, pwd, accessToken)
    
    return api

if __name__ == '__main__':
    shoonya_obj = login()
    exch = 'NSE'
    token = '26000'  # acc stock
    
    try:
        while True:
            if keyboard.is_pressed('esc'):  # Check if the 'Esc' key is pressed
                print("Escape key pressed. Exiting...")
                break

            ret = shoonya_obj.get_quotes(exchange=exch, token=token)
            print(ret)
            time.sleep(2)
    except KeyboardInterrupt:
        print("\nProcess interrupted by the user. Exiting gracefully...")
