import os

def dump_all_python_code(folder_path=".", output_file="all_code_dump.txt"):
    """
    Reads all .py files in the folder and subfolders,
    and writes their full content into a single .txt file.

    Args:
        folder_path (str): Folder to scan. Default is current folder.
        output_file (str): Output text file name.
    """
    with open(output_file, "w", encoding="utf-8") as out:
        for root, _, files in os.walk(folder_path):
            for file in files:
                if file.endswith(".py"):
                    full_path = os.path.join(root, file)
                    try:
                        with open(full_path, "r", encoding="utf-8") as f:
                            code = f.read()
                            out.write(f"\n\n# ===== File: {full_path} =====\n\n")
                            out.write(code)
                    except Exception as e:
                        print(f"⚠️ Could not read {full_path}: {e}")

    print(f"✅ All Python code saved to {output_file}")

# Run it
dump_all_python_code(".", "all_code_dump.txt")