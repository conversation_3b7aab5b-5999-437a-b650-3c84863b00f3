from NorenRestApiPy.NorenApi import NorenApi
import json
import requests as rq
import pandas as pd
import time
import datetime as dt
from pyotp import TOTP
import keyboard  # For detecting key press

def login():
    class ShoonyaApiPy(NorenApi):
        def __init__(self):
            NorenApi.__init__(self, host='https://api.shoonya.com/NorenWClientTP/',
                              websocket='wss://api.shoonya.com/NorenWSTP/')
    
    api = ShoonyaApiPy()
    with open("Cread.json", "r") as f:
        Cread = json.load(f)
    
    user = Cread["user_id"]
    pwd = Cread["password"]
    
    accessToken = open('session_token.txt', 'r').read()
    api.set_session(user, pwd, accessToken)
    
    return api

if __name__ == '__main__':
    shoonya_obj = login()
    exch = 'NSE'
    token = '26000'  # acc stock
    
    try:
        while True:
            if keyboard.is_pressed('esc'):  # Check if the 'Esc' key is pressed
                print("Escape key pressed. Exiting...")
                break

            ret = shoonya_obj.get_quotes(exchange=exch, token=token)
            print(ret['lp'])
            time.sleep(2)
    except KeyboardInterrupt:
        print("\nProcess interrupted by the user. Exiting gracefully...")


print(ret['lp'])

from NorenRestApiPy.NorenApi import NorenApi
import json
import requests as rq
import pandas as pd
import time
import datetime as dt
from pyotp import TOTP
import keyboard  # For detecting key press

def login():
    class ShoonyaApiPy(NorenApi):
        def __init__(self):
            NorenApi.__init__(self, host='https://api.shoonya.com/NorenWClientTP/',
                              websocket='wss://api.shoonya.com/NorenWSTP/')
    
    api = ShoonyaApiPy()
    with open("Cread.json", "r") as f:
        Cread = json.load(f)
    
    user = Cread["user_id"]
    pwd = Cread["password"]
    
    accessToken = open('session_token.txt', 'r').read()
    api.set_session(user, pwd, accessToken)
    
    return api

if __name__ == '__main__':
    shoonya_obj = login()
    exch = 'NSE'
    token = '26000'  # acc stock
    
    try:
        while True:
            if keyboard.is_pressed('esc'):  # Check if the 'Esc' key is pressed
                print("Escape key pressed. Exiting...")
                break

            ret = shoonya_obj.get_quotes(exchange=exch, token=token)
            print(ret)
            time.sleep(2)
    except KeyboardInterrupt:
        print("\nProcess interrupted by the user. Exiting gracefully...")


import pandas as pd

# Sample option chain data (replace with live API or CSV input)
data = [
    {"Strike": 24750, "CE_OI": 45520, "CE_ChgOI": 18.71, "CE_LTP": 30.00, "PE_OI": 35000, "PE_ChgOI": 2.82, "PE_LTP": 30.00},
    {"Strike": 24800, "CE_OI": 19100, "CE_ChgOI": 12.71, "CE_LTP": 14.65, "PE_OI": 30000, "PE_ChgOI": 0.00, "PE_LTP": 35.00},
    {"Strike": 24950, "CE_OI": 33200, "CE_ChgOI": 4.75, "CE_LTP": 3.00, "PE_OI": 0, "PE_ChgOI": 0.00, "PE_LTP": 0.00},
    {"Strike": 25000, "CE_OI": 16530, "CE_ChgOI": 3.80, "CE_LTP": 2.00, "PE_OI": 27500, "PE_ChgOI": 0.00, "PE_LTP": 25.00},
]

df = pd.DataFrame(data)
spot_price = 24967.45

# Identify dominant CE strike
ce_df = df[df["Strike"] < spot_price]
ce_df = ce_df.sort_values(by=["CE_OI", "CE_ChgOI"], ascending=False)
best_ce = ce_df.iloc[0]

# Identify dominant PE strike
pe_df = df[df["Strike"] > spot_price]
pe_df = pe_df.sort_values(by=["PE_OI", "PE_ChgOI"], ascending=False)
best_pe = pe_df.iloc[0] if not pe_df.empty else None

# Generate signal
def generate_signal(strike, ltp, direction):
    sl = round(ltp * 0.75, 2)
    tgt = round(ltp * 1.5, 2)
    return {
        "Direction": direction,
        "Strike": strike,
        "Buy Price": ltp,
        "Stop-loss": sl,
        "Target": tgt
    }

signals = []

if best_ce["CE_OI"] > 30000 and best_ce["CE_ChgOI"] > 10:
    signals.append(generate_signal(best_ce["Strike"], best_ce["CE_LTP"], "Bullish (Buy CE)"))

if best_pe is not None and best_pe["PE_OI"] > 25000 and best_pe["PE_ChgOI"] > 5:
    signals.append(generate_signal(best_pe["Strike"], best_pe["PE_LTP"], "Bearish (Buy PE)"))

# Display signals
for sig in signals:
    print(f"\n📈 Signal: {sig['Direction']}")
    print(f"Strike Price: {sig['Strike']}")
    print(f"Buy at: ₹{sig['Buy Price']}")
    print(f"Stop-loss: ₹{sig['Stop-loss']}")
    print(f"Target: ₹{sig['Target']}")

# Step 1: Import libraries
from sklearn.linear_model import LogisticRegression
import pandas as pd

# Step 2: Prepare sample data with direction
data = {
    'body_ratio': [0.8, 0.3, 0.6, 0.1, 0.9],
    'wick_symmetry': [0.2, 0.5, 0.1, 0.6, 0.0],
    'direction_flag': [1, 0, 1, 0, 1],  # 1 = Open < Close (bullish), 0 = Open > Close (bearish)
    'label': [1, 0, 1, 0, 1]  # 1 = bullish, 0 = bearish
}
df = pd.DataFrame(data)

# Step 3: Split features and labels
X = df[['body_ratio', 'wick_symmetry', 'direction_flag']]
y = df['label']

# Step 4: Train model
model = LogisticRegression()
model.fit(X, y)

# Step 5: Test prediction
test_candle = pd.DataFrame([[0.7, 0.1, 1]], columns=['body_ratio', 'wick_symmetry', 'direction_flag'])
prediction = model.predict(test_candle)
probability = model.predict_proba(test_candle)

print("Prediction:", "bullish" if prediction[0] == 1 else "bearish")
print("Confidence:", round(probability[0][1] * 100, 2), "%")

# Step 1: Import libraries
import pandas as pd
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense
import numpy as np

# Step 2: Prepare sample data
data = {
    'body_ratio': [0.8, 0.3, 0.6, 0.1, 0.9],
    'wick_symmetry': [0.2, 0.5, 0.1, 0.6, 0.0],
    'direction_flag': [1, 0, 1, 0, 1],
    'trap_label': [0, 1, 0, 1, 0],  # 1 = trap, 0 = clean
    'bullish_label': [1, 0, 1, 0, 1]  # 1 = bullish, 0 = bearish
}
df = pd.DataFrame(data)

# Step 3: Split features
X_class = df[['body_ratio', 'wick_symmetry', 'direction_flag']]
y_class = df['bullish_label']

X_trap = df[['wick_symmetry']]
y_trap = df['trap_label']

# Step 4: Train Logistic Regression
log_model = LogisticRegression()
log_model.fit(X_class, y_class)

# Step 5: Train SVM for trap detection
svm_model = SVC(probability=True)
svm_model.fit(X_trap, y_trap)

# Step 6: Prepare sequence data for LSTM
sequence_data = np.array([
    [[0.8], [0.2], [1]],
    [[0.3], [0.5], [0]],
    [[0.6], [0.1], [1]],
    [[0.1], [0.6], [0]],
    [[0.9], [0.0], [1]]
])
sequence_labels = np.array([1, 0, 1, 0, 1])  # bullish pattern

# Step 7: Build LSTM model
lstm_model = Sequential()
lstm_model.add(LSTM(10, input_shape=(3, 1)))
lstm_model.add(Dense(1, activation='sigmoid'))
lstm_model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
lstm_model.fit(sequence_data, sequence_labels, epochs=10, verbose=0)

# Step 8: Test all models
test_candle = pd.DataFrame([[0.7, 0.1, 1]], columns=['body_ratio', 'wick_symmetry', 'direction_flag'])

# Logistic Regression
log_pred = log_model.predict(test_candle)
log_conf = log_model.predict_proba(test_candle)[0][1]

# SVM
svm_pred = svm_model.predict(test_candle[['wick_symmetry']])
svm_conf = svm_model.predict_proba(test_candle[['wick_symmetry']])[0][1]

# LSTM
test_seq = np.array([[[0.7], [0.1], [1]]])
lstm_pred = lstm_model.predict(test_seq, verbose=0)[0][0]

# Final Output
print("Logistic Prediction:", "bullish" if log_pred[0] == 1 else "bearish", "| Confidence:", round(log_conf * 100, 2), "%")
print("SVM Trap Detection:", "trap" if svm_pred[0] == 1 else "clean", "| Confidence:", round(svm_conf * 100, 2), "%")
print("LSTM Pattern Score:", round(lstm_pred * 100, 2), "%")