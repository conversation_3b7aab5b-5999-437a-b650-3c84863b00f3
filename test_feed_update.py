from NorenRestApiPy.NorenApi import NorenApi
import json
import time
import sys
import keyboard
import datetime

# Login using session token
def login():
    class ShoonyaApiPy(NorenApi):
        def __init__(self):
            super().__init__(host='https://api.shoonya.com/NorenWClientTP/',
                             websocket='wss://api.shoonya.com/NorenWSTP/')

    api = ShoonyaApiPy()

    try:
        with open("Cread.json", "r") as f:
            Cread = json.load(f)
        user = Cread["user_id"]
        pwd = Cread["password"]
        accessToken = open('session_token.txt', 'r').read().strip()
        api.set_session(user, pwd, accessToken)
        return api
    except Exception as e:
        print(f"[Login] Error: {e}")
        sys.exit(1)

# Main loop
def monitor_feed(api, exch='NSE', token='26000'):
    print("[Feed Monitor] Press ESC to exit...\n")
    try:
        while True:
            if keyboard.is_pressed('esc'):
                print("\n[Feed Monitor] Escape key pressed. Exiting...")
                break

            ret = api.get_quotes(exchange=exch, token=token)
            lp = ret.get('lp', 'N/A')
            lut_raw = ret.get('lut', None)

            if lut_raw:
                try:
                    lut_dt = datetime.datetime.fromtimestamp(int(lut_raw))
                    lut_str = lut_dt.strftime('%H:%M:%S %d-%m-%Y')
                except Exception:
                    lut_str = 'Invalid timestamp'
            else:
                lut_str = 'N/A'

            sys.stdout.write(f"\r[Feed] Server Time: {lut_str} | Price: ₹{lp}   ")
            sys.stdout.flush()
            time.sleep(2)
    except KeyboardInterrupt:
        print("\n[Feed Monitor] Interrupted by user. Exiting...")

# Run
if __name__ == '__main__':
    shoonya_obj = login()
    monitor_feed(shoonya_obj)