import threading
import time
import json
import sys
from pyotp import TOTP
from NorenRestApiPy.NorenApi import NorenApi

# Shared state
latest_price = None
price_lock = threading.Lock()
stop_threads = {"quotes": False, "order": False}
qty = 25
pdt = 'I' # C for cash, I for MIS

# Login function
def login():
    class ShoonyaApiPy(NorenApi):
        def __init__(self):
            super().__init__(host='https://api.shoonya.com/NorenWClientTP/',
                             websocket='wss://api.shoonya.com/NorenWSTP/')

    api = ShoonyaApiPy()

    try:
        with open("Cread.json", "r") as f:
            Cread = json.load(f)
    except Exception as e:
        print(f"[Login] Failed to read Cread.json: {e}")
        sys.exit(1)

    required_keys = ["user_id", "password", "vendor_code", "app_key", "imei", "totp"]
    for key in required_keys:
        if key not in Cread:
            raise KeyError(f"Missing key in Cread.json: {key}")

    user = Cread["user_id"]
    pwd = Cread["password"]
    vc = Cread["vendor_code"]
    app_key = Cread["app_key"]
    imei = Cread["imei"]
    totp = TOTP(Cread["totp"]).now()

    login_resp = api.login(userid=user, password=pwd, twoFA=totp,
                           vendor_code=vc, api_secret=app_key, imei=imei)

    if login_resp.get('stat') != 'Ok':
        print("[Login] Failed:", login_resp)
        sys.exit(1)

    print("[Login] Successful")
    return api

# Thread 1: Fetch quotes
def quote_fetcher(api, exch, token):
    global latest_price
    while not stop_threads["quotes"]:
        try:
            ret = api.get_quotes(exchange=exch, token=token)
            with price_lock:
                latest_price = float(ret['lp'])
            sys.stdout.write(f"\r[Quote Thread] Latest Price: {latest_price:.2f}   ")
            sys.stdout.flush()
            time.sleep(2)
        except Exception as e:
            sys.stdout.write(f"\r[Quote Thread] Error: {e}   ")
            sys.stdout.flush()
            time.sleep(2)

# Thread 2: Place order based on price
def order_executor(api, ts, exg, threshold, force=False):
    while not stop_threads["order"]:
        with price_lock:
            lp = latest_price
        if lp and (force or lp < threshold):
            print(f"\n[Order Thread] Triggering Buy Order at {lp}")
            try:
                op_buyorder_mkt = api.place_order(
                    buy_or_sell='B', product_type=pdt,
                    exchange=exg, tradingsymbol=ts,
                    quantity=qty, discloseqty=0, price_type='MKT',
                    price=0, trigger_price=None,
                    retention='DAY', remarks='my_order_001'
                )
                print(f"[Order Thread] Order Response: {op_buyorder_mkt}")
                stop_threads["order"] = True
            except Exception as e:
                print(f"[Order Thread] Error placing order: {e}")
        time.sleep(1)

# Main control loop
def main():
    api = login()
    exch = 'NSE'
    token = '26000'       # ACC stock token
    ts = 'TCS-EQ'         # Trading symbol
    threshold_price = 26000  # Adjust this to test auto trigger

    # Start quote thread
    t1 = threading.Thread(target=quote_fetcher, args=(api, exch, token))
    t1.start()

    print("\nOptions:\n 1 - Start Order Thread (auto)\n 2 - Force Order Now\n 3 - Stop Quote Thread\n 4 - Stop Order Thread\n 5 - Exit All\n")

    try:
        while True:
            choice = input("\nEnter your choice: ").strip()
            if choice == '1':
                if not stop_threads["order"]:
                    print("[Main] Starting order thread (auto trigger)...")
                    t2 = threading.Thread(target=order_executor, args=(api, ts, exch, threshold_price))
                    t2.start()
                else:
                    print("[Main] Order thread already stopped.")
            elif choice == '2':
                print("[Main] Forcing order now...")
                stop_threads["order"] = False
                threading.Thread(target=order_executor, args=(api, ts, exch, threshold_price, True)).start()
            elif choice == '3':
                stop_threads["quotes"] = True
                print("[Main] Quote thread stopping...")
            elif choice == '4':
                stop_threads["order"] = True
                print("[Main] Order thread stopping...")
            elif choice == '5':
                stop_threads["quotes"] = True
                stop_threads["order"] = True
                print("[Main] Exiting all threads...")
                break
            else:
                print("[Main] Invalid choice. Try again.")
            time.sleep(0.5)
    except KeyboardInterrupt:
        print("\n[Main] Interrupted. Exiting...")

    t1.join()
    print("[Main] All threads stopped. Program terminated.")

if __name__ == '__main__':
    main()