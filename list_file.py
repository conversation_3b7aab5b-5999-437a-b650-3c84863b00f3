import os

def list_files_in_folder(folder_path="."):
    """
    Lists all files in the given folder and its subfolders.

    Args:
        folder_path (str): Path to the folder. Default is current folder.

    Returns:
        list: List of file paths.
    """
    file_list = []
    for root, _, files in os.walk(folder_path):
        for file in files:
            full_path = os.path.join(root, file)
            file_list.append(full_path)
    return file_list

# Run and display
files = list_files_in_folder(".")
for f in files:
    print(f)